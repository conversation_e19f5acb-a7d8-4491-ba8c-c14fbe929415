# 卡牌收藏页面滚动按钮音效功能

## 功能概述

为卡牌收藏页面的左右滚动按钮添加了专门的点击音效，与卡牌滚动音效区分开来，提供更丰富的音频反馈。

## 实现特点

### 🔊 音效区分
- **按钮点击音效**: `res://assert/audio/sfx/button_click001.mp3`
- **卡牌滚动音效**: `res://assert/audio/sfx/flip01.mp3`
- **独立触发**: 按钮点击和卡牌滚动音效分别触发，互不干扰

### 🎯 触发时机
- **立即播放**: 点击按钮时立即播放音效
- **动画前播放**: 在按钮动画之前播放，确保音效响应及时
- **滚动前播放**: 在实际滚动卡牌之前播放，提供即时反馈

## 技术实现

### 新增函数
```gdscript
func _play_scroll_button_click_sfx():
    var audio_manager = get_node_or_null("/root/AudioManager")
    if audio_manager:
        audio_manager.play_sfx("res://assert/audio/sfx/button_click001.mp3")
    else:
        print("警告：无法获取AudioManager，无法播放按钮点击音效")
```

### 修改的函数
1. **`_on_left_button_pressed()`** - 添加按钮点击音效
2. **`_on_right_button_pressed()`** - 添加按钮点击音效

## 音效播放顺序

当用户点击左右滚动按钮时，音效播放顺序如下：

1. **按钮点击音效** (`button_click001.mp3`) - 立即播放
2. **按钮动画** - 视觉反馈
3. **卡牌滚动** - 实际滚动操作
4. **卡牌滚动音效** (`flip01.mp3`) - 当中心卡牌变化时播放

## 用户体验

### 🎮 交互反馈层次
- **即时反馈**: 点击按钮立即听到点击音效
- **操作确认**: 按钮音效确认用户操作被接收
- **结果反馈**: 卡牌滚动音效确认操作结果

### 🔊 音效特点
- **button_click001.mp3**: 清脆的按钮点击声，适合UI交互
- **flip01.mp3**: 卡牌翻转声，适合卡牌切换场景

## 与其他功能的协调

### ✅ 兼容性
- 与现有的卡牌滚动音效完全兼容
- 不影响其他按钮的音效
- 与按钮动画效果协调工作
- 支持AudioManager的音量控制和静音功能

### 🚫 冲突避免
- 按钮音效没有冷却限制（因为按钮本身有防重复点击机制）
- 与卡牌滚动音效使用不同的音频文件
- 在飞入动画期间按钮被禁用，避免音效冲突

## 使用场景

### 会播放按钮音效的情况
- 点击左滚动按钮 (◀)
- 点击右滚动按钮 (▶)
- 按钮可用且不在飞入动画期间

### 不会播放按钮音效的情况
- 使用鼠标滚轮滚动（只播放卡牌滚动音效）
- 拖拽滚动（只播放卡牌滚动音效）
- 飞入动画期间（按钮被禁用）

## 代码修改位置

### script/card_collection.gd
```gdscript
# 左滚动按钮点击
func _on_left_button_pressed():
    if is_playing_fly_in_animation:
        return
    
    _play_scroll_button_click_sfx()  # 新增：播放按钮音效
    _play_scroll_button_animation(left_scroll_button)
    _scroll_cards(-1)

# 右滚动按钮点击  
func _on_right_button_pressed():
    if is_playing_fly_in_animation:
        return
    
    _play_scroll_button_click_sfx()  # 新增：播放按钮音效
    _play_scroll_button_animation(right_scroll_button)
    _scroll_cards(1)
```

## 调试和测试

### 测试步骤
1. 进入卡牌收藏页面
2. 等待飞入动画完成
3. 点击左右滚动按钮
4. 验证音效播放顺序：按钮点击音效 → 卡牌滚动音效

### 预期效果
- 每次点击按钮都能听到清脆的点击音效
- 卡牌切换时能听到翻转音效
- 两种音效层次分明，不会混淆
