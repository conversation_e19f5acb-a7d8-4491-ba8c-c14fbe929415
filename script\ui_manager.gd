extends Node

# UI管理器 - 全局单例，管理UI样式和动画

# 主题资源
var game_theme = null  # 将在_ready中动态加载

# 初始化
func _ready():
    # 监听场景树变化，以便在场景切换时应用字体和主题
    get_tree().node_added.connect(_on_node_added)
    
    # 动态加载主题文件
    var theme_path = "res://themes/game_theme.tres"
    if ResourceLoader.exists(theme_path):
        game_theme = ResourceLoader.load(theme_path)
        # 应用主题到当前场景
        if get_tree().current_scene:
            apply_theme_recursive(get_tree().current_scene, game_theme)
            print("UI管理器: 已将主题应用到当前场景 - ", get_tree().current_scene.name)
    else:
        push_error("UI管理器: 找不到主题文件，请确认路径: " + theme_path)
        # 使用备用主题
        game_theme = _create_fallback_theme()
    
    # 应用思源字体到当前场景
    var current_scene = get_tree().current_scene
    if current_scene:
        apply_siyuan_font_recursive(current_scene)
        print("UI管理器: 已将思源字体应用到当前场景 - ", current_scene.name)
    
    # 设置所有按钮的悬停效果
    if current_scene:
        _setup_buttons_recursive(current_scene)

# 创建备用主题
func _create_fallback_theme():
    # 创建一个简单的备用主题
    var theme = Theme.new()
    
    # 尝试加载思源字体作为备用字体
    var font_path = "res://fonts/siyuan.ttf"
    var font = null
    
    if ResourceLoader.exists(font_path):
        font = ResourceLoader.load(font_path) as FontFile
        if font:
            theme.default_font = font
            print("UI管理器: 已加载思源字体作为备用字体")
    
    # 创建基本的按钮样式
    var button_normal = StyleBoxFlat.new()
    button_normal.bg_color = Color(0.2, 0.3, 0.5, 0.8)
    button_normal.corner_radius_top_left = 8
    button_normal.corner_radius_top_right = 8
    button_normal.corner_radius_bottom_left = 8
    button_normal.corner_radius_bottom_right = 8
    
    var button_hover = StyleBoxFlat.new()
    button_hover.bg_color = Color(0.3, 0.4, 0.6, 0.8)
    button_hover.corner_radius_top_left = 8
    button_hover.corner_radius_top_right = 8
    button_hover.corner_radius_bottom_left = 8
    button_hover.corner_radius_bottom_right = 8
    
    # 设置按钮样式
    theme.set_stylebox("normal", "Button", button_normal)
    theme.set_stylebox("hover", "Button", button_hover)
    theme.set_stylebox("pressed", "Button", button_hover)
    
    # 设置按钮字体大小
    theme.set_font_size("font_size", "Button", 22)
    theme.default_font_size = 18
    
    return theme

# 应用全局主题设置
func apply_global_theme_settings():
    # 设置默认主题资源
    theme_override_font()
    
    # 尝试应用思源字体到场景树
    get_tree().call_group("needs_siyuan_font", "apply_siyuan_font_from_group")
    
    # 实际游戏中可以添加更多全局主题设置

# 应用主题到场景
func apply_theme_to_scene(scene_root):
    if not scene_root:
        return
    
    # 递归应用主题到场景中的所有节点
    _apply_theme_recursive(scene_root)
    
    # 设置所有按钮的悬停效果
    _setup_buttons_recursive(scene_root)

# 初始化场景UI
func init_scene_ui(scene_root):
    # 应用主题
    apply_theme_to_scene(scene_root)
    
    # 初始化UI特效
    # 如需更多特效，可在具体场景中调用
    
    print("UI管理器: 初始化场景UI - ", scene_root.name if scene_root else "未知场景")

# 主题覆盖字体
func theme_override_font():
    # 尝试加载思源字体
    var font_path = "res://fonts/siyuan.ttf"
    if ResourceLoader.exists(font_path):
        var font = ResourceLoader.load(font_path) as FontFile
        if font:
            # 可以在这里应用字体到特定的控件或场景
            print("UI管理器: 已加载思源字体用于全局覆盖")
            
            # 添加主要场景节点到思源字体组
            _add_main_scenes_to_siyuan_font_group()

# 递归应用主题
func _apply_theme_recursive(node):
    apply_theme_recursive(node, game_theme)

# 递归设置按钮效果
func _setup_buttons_recursive(node):
    if not node:
        return
    
    # 如果是按钮，添加悬停效果
    if node is Button:
        _setup_button(node)
    
    # 递归处理所有子节点
    for child in node.get_children():
        _setup_buttons_recursive(child)

# 设置按钮效果
func _setup_button(button):
    if not button:
        return

    # 跳过自定义按钮（如标签页按钮）
    if button.has_meta("custom_button"):
        return

    # 确保按钮有正确的缩放中心点
    button.pivot_offset = button.size / 2

    # 添加悬停效果
    if not button.is_connected("mouse_entered", _on_button_mouse_entered):
        button.mouse_entered.connect(_on_button_mouse_entered.bind(button))
    if not button.is_connected("mouse_exited", _on_button_mouse_exited):
        button.mouse_exited.connect(_on_button_mouse_exited.bind(button))
    if not button.is_connected("button_down", _on_button_pressed):
        if button.is_connected("pressed", _on_button_pressed):
            button.pressed.disconnect(_on_button_pressed)
        button.button_down.connect(_on_button_pressed.bind(button))

# 按钮鼠标进入
func _on_button_mouse_entered(button):
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_ELASTIC)
    tween.tween_property(button, "scale", Vector2(1.05, 1.05), 0.3)
    
    # 播放悬停音效
    # AudioManager.play_sound("ui_hover")

# 按钮鼠标离开
func _on_button_mouse_exited(button):
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_ELASTIC)
    tween.tween_property(button, "scale", Vector2(1.0, 1.0), 0.3)

# 按钮按下 (现在由 button_down 触发)
func _on_button_pressed(button):
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_ELASTIC)
    tween.tween_property(button, "scale", Vector2(0.95, 0.95), 0.1)
    
    # 播放按钮效果（已禁用闪光效果）
    # _play_button_effect(button)
    
    # 播放点击音效
    var audio_manager = get_node("/root/AudioManager")
    if audio_manager:
        audio_manager.play_button_click_sfx()

# 播放按钮特效
func _play_button_effect(button):
    # 创建闪光效果
    var flash = ColorRect.new()
    flash.color = Color(1, 1, 1, 0.5)
    flash.size = button.size
    flash.position = Vector2(0, 0)
    flash.mouse_filter = Control.MOUSE_FILTER_IGNORE
    button.add_child(flash)
    
    # 创建淡出动画
    var tween = create_tween()
    tween.tween_property(flash, "modulate:a", 0.0, 0.2)
    
    # 完成后清理
    await tween.finished
    flash.queue_free()

# 创建面板淡入动画
func create_panel_fade_in(panel, duration: float = 0.3):
    if not panel:
        return null
    
    # 确保面板可见
    panel.visible = true
    panel.modulate.a = 0.0
    
    # 创建动画
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_CUBIC)
    tween.tween_property(panel, "modulate:a", 1.0, duration)
    
    return tween

# 创建面板淡出动画
func create_panel_fade_out(panel, duration: float = 0.3):
    if not panel:
        return null
    
    # 创建动画
    var tween = create_tween()
    tween.set_ease(Tween.EASE_IN)
    tween.set_trans(Tween.TRANS_CUBIC)
    tween.tween_property(panel, "modulate:a", 0.0, duration)
    
    # 完成后隐藏面板
    await tween.finished
    panel.visible = false
    
    return tween

# 创建面板弹出动画
func create_panel_popup(panel, duration: float = 0.5):
    if not panel:
        return null
    
    # 确保面板有正确的缩放中心
    if panel is Control:
        panel.pivot_offset = panel.size / 2
    
    # 设置初始状态
    panel.visible = true
    panel.modulate.a = 0.0
    panel.scale = Vector2(0.5, 0.5)
    
    # 确保面板样式中有圆角设置
    if panel is Panel:
        # 获取当前样式
        var current_style = panel.get_theme_stylebox("panel")
        if current_style is StyleBoxFlat:
            # 确保有圆角设置
            if current_style.corner_radius_top_left == 0 and current_style.corner_radius_top_right == 0 and current_style.corner_radius_bottom_left == 0 and current_style.corner_radius_bottom_right == 0:
                # 如果没有圆角，添加圆角
                var new_style = current_style.duplicate()
                new_style.corner_radius_top_left = 20
                new_style.corner_radius_top_right = 20
                new_style.corner_radius_bottom_left = 20
                new_style.corner_radius_bottom_right = 20
                new_style.shadow_size = 8
                panel.add_theme_stylebox_override("panel", new_style)
    
    # 创建动画
    var tween = create_tween()
    tween.set_ease(Tween.EASE_OUT)
    tween.set_trans(Tween.TRANS_ELASTIC)
    tween.tween_property(panel, "scale", Vector2(1.1, 1.1), duration * 0.6)
    tween.parallel().tween_property(panel, "modulate:a", 1.0, duration * 0.4)
    tween.tween_property(panel, "scale", Vector2(1.0, 1.0), duration * 0.4)
    
    return tween

# 应用思源字体到控件
func apply_siyuan_font(control, size = null):
    if not control:
        return
    
    # 加载思源字体
    var font_path = "res://fonts/siyuan.ttf"
    if ResourceLoader.exists(font_path):
        var font = ResourceLoader.load(font_path) as FontFile
        if font:
            # 根据控件类型应用字体
            if control is Label:
                control.add_theme_font_override("font", font)
                if size != null:
                    control.add_theme_font_size_override("font_size", size)
            elif control is Button:
                control.add_theme_font_override("font", font)
                if size != null:
                    control.add_theme_font_size_override("font_size", size)
            elif control is LineEdit:
                control.add_theme_font_override("font", font)
                if size != null:
                    control.add_theme_font_size_override("font_size", size)
            elif control is TextEdit:
                control.add_theme_font_override("font", font)
                if size != null:
                    control.add_theme_font_size_override("font_size", size)
            elif control is RichTextLabel:
                control.add_theme_font_override("normal_font", font)
                if size != null:
                    control.add_theme_font_size_override("normal_font_size", size)
            
            # 其他类型的控件可以在这里添加
            
            return true
    
    return false

# 递归应用思源字体到节点及其子节点
func apply_siyuan_font_recursive(node, size = null):
    if not node:
        return
    
    # 应用字体到当前节点（如果是Control类型）
    if node is Control:
        apply_siyuan_font(node, size)
    
    # 递归应用到所有子节点
    for child in node.get_children():
        apply_siyuan_font_recursive(child, size)

# 添加主要场景节点到思源字体组
func _add_main_scenes_to_siyuan_font_group():
    # 在下一帧处理，确保场景已经加载
    call_deferred("_deferred_add_scenes_to_font_group")

# 延迟添加场景到字体组
func _deferred_add_scenes_to_font_group():
    # 等待一帧确保场景树已加载
    await get_tree().process_frame
    
    # 尝试查找主要场景节点
    var main_menu = get_node_or_null("/root/MainMenu")
    if main_menu:
        main_menu.add_to_group("needs_siyuan_font")
        print("UI管理器: 已将主菜单添加到思源字体组")
    
    # 尝试查找游戏场景
    var game_scene = get_node_or_null("/root/GameScene") 
    if game_scene:
        game_scene.add_to_group("needs_siyuan_font")
        print("UI管理器: 已将游戏场景添加到思源字体组")
    
    # 直接应用思源字体到当前活动场景
    var current_scene = get_tree().current_scene
    if current_scene:
        apply_siyuan_font_recursive(current_scene)
        print("UI管理器: 已将思源字体应用到当前场景 - ", current_scene.name)

# 场景树变化处理
func _on_node_added(node):
    if node is Control:
        # 应用思源字体
        if node.is_in_group("needs_siyuan_font"):
            apply_siyuan_font(node)
            print("UI管理器: 已将思源字体应用到新添加的场景节点 - ", node.name)
        
        # 应用主题
        if node.is_in_group("needs_theme"):
            apply_theme_recursive(node, game_theme)
            print("UI管理器: 已将主题应用到新添加的场景节点 - ", node.name)
        
        # 如果是按钮，设置悬停效果
        if node is Button:
            _setup_button(node)

# 递归应用主题到场景树
func apply_theme_recursive(node, theme):
    if not node or not theme:
        return
        
    if node is Control:
        node.theme = theme
    
    for child in node.get_children():
        apply_theme_recursive(child, theme) 
