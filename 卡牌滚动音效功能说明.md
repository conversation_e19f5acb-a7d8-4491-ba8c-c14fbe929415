# 卡牌收藏页面滚动音效功能

## 功能概述

为卡牌收藏页面添加了滚动音效功能，当用户滚动卡牌时，每次卡牌切换都会播放音效，增强用户交互体验。

## 实现特点

### 🔊 音效触发机制
- **中心卡牌检测**: 监测当前处于中心位置的卡牌索引变化
- **每张卡牌播放**: 每当中心卡牌发生变化时播放一次音效
- **多种滚动方式支持**: 支持鼠标滚轮、左右按钮、拖拽等所有滚动方式

### ⚡ 性能优化
- **音效冷却**: 0.1秒的冷却时间，防止快速滚动时音效过于频繁
- **飞入动画排除**: 在卡牌飞入动画期间不播放滚动音效
- **初始化排除**: 页面初始化时不播放音效

### 🎵 音效文件
- **音效路径**: `res://assert/audio/sfx/flip01.mp3`
- **音效类型**: 卡牌翻转音效，适合滚动场景

## 技术实现

### 新增变量
```gdscript
# 音效控制变量
var last_center_card_index = -1  # 上一次的中心卡牌索引
var last_scroll_sfx_time = 0.0   # 上一次播放音效的时间
var scroll_sfx_cooldown = 0.1    # 音效冷却时间（秒）
```

### 新增函数
1. **`_check_center_card_change()`** - 检测中心卡牌变化
2. **`_play_card_scroll_sfx()`** - 播放滚动音效（带冷却机制）

### 修改的函数
- **`_update_card_positions()`** - 添加中心卡牌变化检测
- **`_initialize_positions()`** - 初始化中心卡牌索引

## 代码实现

### 中心卡牌变化检测
```gdscript
func _check_center_card_change():
    # 如果正在播放飞入动画，不播放滚动音效
    if is_playing_fly_in_animation:
        return
    
    var current_center_index = _get_current_center_card_index()
    
    # 如果中心卡牌发生了变化，播放音效
    if current_center_index != last_center_card_index and last_center_card_index != -1:
        _play_card_scroll_sfx()
    
    # 更新记录的中心卡牌索引
    last_center_card_index = current_center_index
```

### 音效播放（带冷却）
```gdscript
func _play_card_scroll_sfx():
    var current_time = Time.get_unix_time_from_system()
    
    # 检查冷却时间
    if current_time - last_scroll_sfx_time < scroll_sfx_cooldown:
        return
    
    var audio_manager = get_node_or_null("/root/AudioManager")
    if audio_manager:
        audio_manager.play_sfx("res://assert/audio/sfx/flip01.mp3")
        last_scroll_sfx_time = current_time
```

## 触发场景

### ✅ 会播放音效的情况
- 使用鼠标滚轮滚动卡牌
- 点击左右滚动按钮
- 拖拽卡牌滚动
- 点击分类标签切换时的自动滚动

### ❌ 不会播放音效的情况
- 页面初始加载时
- 卡牌飞入动画期间
- 冷却时间内的重复触发
- 中心卡牌没有实际变化时

## 用户体验

### 🎯 交互反馈
- 每次卡牌切换都有清晰的音效反馈
- 音效与视觉动画同步，增强沉浸感
- 适度的音效频率，不会过于嘈杂

### 🔧 可调整参数
- **冷却时间**: 可通过修改 `scroll_sfx_cooldown` 调整（默认0.1秒）
- **音效文件**: 可替换为其他音效文件
- **触发条件**: 可修改检测逻辑调整触发时机

## 兼容性

- 与现有的AudioManager系统完全兼容
- 不影响其他音效的播放
- 支持音量控制和静音功能
- 与卡牌旋转、缩放等其他效果协调工作
