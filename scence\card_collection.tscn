[gd_scene load_steps=11 format=3 uid="uid://bxnv6cucb8e67"]

[ext_resource type="Script" uid="uid://dgusqsen8ssfr" path="res://script/card_collection.gd" id="1_main"]
[ext_resource type="Texture2D" uid="uid://bq6www62jvdn2" path="res://assert/collection/back1.png" id="2_3g31u"]
[ext_resource type="Texture2D" uid="uid://bu5lxeyenk77a" path="res://assert/collection/right_button.png" id="3_8r0tg"]
[ext_resource type="Texture2D" uid="uid://b1xffdi4g7j6t" path="res://assert/collection/detail_panel1.png" id="3_b17ul"]
[ext_resource type="Shader" uid="uid://dngrfo36btbbw" path="res://shader/card_center_glow.gdshader" id="3_uccas"]
[ext_resource type="Texture2D" uid="uid://b0wpjgqws7xwo" path="res://themes/collection_card_back.tres" id="4_00edu"]
[ext_resource type="Texture2D" uid="uid://eo72plfipvvv" path="res://assert/collection/button1.png" id="5_5e67x"]

[sub_resource type="ButtonGroup" id="ButtonGroup_1"]

[sub_resource type="ShaderMaterial" id="ShaderMaterial_em33l"]
shader = ExtResource("3_uccas")
shader_parameter/gradient = ExtResource("4_00edu")
shader_parameter/glow_intensity = 0.05
shader_parameter/spread = 1.0
shader_parameter/cutoff = 0.2
shader_parameter/size = -0.7
shader_parameter/speed = 1.0
shader_parameter/ray1_density = 8.0
shader_parameter/ray2_density = 30.0
shader_parameter/ray2_intensity = 1.0
shader_parameter/core_intensity = 15.0
shader_parameter/hdr = true
shader_parameter/seed = 10.0

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_1"]
bg_color = Color(0, 0, 0, 0.7)
corner_radius_top_left = 10
corner_radius_top_right = 10
corner_radius_bottom_right = 10
corner_radius_bottom_left = 10

[node name="CardCollection" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_main")

[node name="Background" type="TextureRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
texture = ExtResource("2_3g31u")
expand_mode = 1
stretch_mode = 4

[node name="MainContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="TitleContainer" type="CenterContainer" parent="MainContainer"]
visible = false
custom_minimum_size = Vector2(0, 100)
layout_mode = 2

[node name="CardCollTitleLabel" type="Label" parent="MainContainer/TitleContainer"]
layout_mode = 2
theme_override_colors/font_color = Color(1, 1, 1, 1)
theme_override_colors/font_shadow_color = Color(0, 0, 0, 0.5)
theme_override_constants/shadow_offset_x = 2
theme_override_constants/shadow_offset_y = 2
theme_override_font_sizes/font_size = 48
text = "CardCollTitleLabel"
horizontal_alignment = 1

[node name="CategoryTabContainer" type="CenterContainer" parent="MainContainer"]
z_index = 1
custom_minimum_size = Vector2(0, 60)
layout_mode = 2

[node name="TabButtonContainer" type="HBoxContainer" parent="MainContainer/CategoryTabContainer"]
layout_mode = 2

[node name="ZooTabButton" type="Button" parent="MainContainer/CategoryTabContainer/TabButtonContainer"]
custom_minimum_size = Vector2(150, 40)
layout_mode = 2
toggle_mode = true
button_pressed = true
button_group = SubResource("ButtonGroup_1")
text = "ZooTabButton"

[node name="RewardsTabButton" type="Button" parent="MainContainer/CategoryTabContainer/TabButtonContainer"]
custom_minimum_size = Vector2(150, 40)
layout_mode = 2
toggle_mode = true
button_group = SubResource("ButtonGroup_1")
text = "RewardsTabButton"

[node name="CardDisplayArea" type="Control" parent="MainContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="CardScrollContainer" type="Control" parent="MainContainer/CardDisplayArea"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="CardContainer" type="Control" parent="MainContainer/CardDisplayArea/CardScrollContainer"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2

[node name="Control" type="Control" parent="MainContainer/CardDisplayArea/CardScrollContainer/CardContainer"]
anchors_preset = 0
offset_left = 488.0
offset_top = 72.0
offset_right = 672.0
offset_bottom = 304.0

[node name="ColorRect" type="ColorRect" parent="MainContainer/CardDisplayArea/CardScrollContainer/CardContainer/Control"]
visible = false
material = SubResource("ShaderMaterial_em33l")
layout_mode = 0
offset_left = -520.0
offset_top = -750.0
offset_right = 680.0
offset_bottom = 1050.0

[node name="CardInfoArea" type="CenterContainer" parent="MainContainer"]
z_index = 1
custom_minimum_size = Vector2(0, 100)
layout_mode = 2

[node name="CardInfoContainer" type="Control" parent="MainContainer/CardInfoArea"]
custom_minimum_size = Vector2(820, 100)
layout_mode = 2

[node name="LeftScrollButton" type="Button" parent="MainContainer/CardInfoArea/CardInfoContainer"]
custom_minimum_size = Vector2(80, 80)
layout_mode = 2
offset_left = -30.0
offset_top = 12.0
offset_right = 50.0
offset_bottom = 92.0
text = "◀"
flat = true

[node name="TextureRect" type="TextureRect" parent="MainContainer/CardInfoArea/CardInfoContainer/LeftScrollButton"]
modulate = Color(0.862745, 0.862745, 0.862745, 1)
layout_mode = 0
offset_left = -10.0
offset_top = -10.0
offset_right = 90.0
offset_bottom = 90.0
texture = ExtResource("3_8r0tg")
expand_mode = 1
stretch_mode = 5
flip_h = true

[node name="CardInfoPanel" type="Panel" parent="MainContainer/CardInfoArea/CardInfoContainer"]
custom_minimum_size = Vector2(600, 100)
layout_mode = 2
offset_left = 110.0
offset_right = 710.0
offset_bottom = 100.0

[node name="Back" type="Sprite2D" parent="MainContainer/CardInfoArea/CardInfoContainer/CardInfoPanel"]
modulate = Color(0.864675, 0.864675, 0.864675, 1)
position = Vector2(300, 47)
scale = Vector2(0.607407, 0.385)
texture = ExtResource("3_b17ul")

[node name="CardInfoVContainer" type="VBoxContainer" parent="MainContainer/CardInfoArea/CardInfoContainer/CardInfoPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = 10.0
offset_right = -20.0
offset_bottom = -10.0
grow_horizontal = 2
grow_vertical = 2

[node name="CardInfoNameLabel" type="Label" parent="MainContainer/CardInfoArea/CardInfoContainer/CardInfoPanel/CardInfoVContainer"]
visible = false
layout_mode = 2
theme_override_colors/font_color = Color(1, 1, 1, 1)
theme_override_font_sizes/font_size = 20
text = "卡牌名称"
horizontal_alignment = 1
vertical_alignment = 1

[node name="CardInfoDescLabel" type="RichTextLabel" parent="MainContainer/CardInfoArea/CardInfoContainer/CardInfoPanel/CardInfoVContainer"]
layout_mode = 2
size_flags_vertical = 3
bbcode_enabled = true
text = "卡牌描述信息"
fit_content = true
scroll_active = false
autowrap_mode = 2
horizontal_alignment = 1
vertical_alignment = 1

[node name="RightScrollButton" type="Button" parent="MainContainer/CardInfoArea/CardInfoContainer"]
custom_minimum_size = Vector2(80, 80)
layout_mode = 2
offset_left = 770.0
offset_top = 12.0
offset_right = 850.0
offset_bottom = 92.0
pivot_offset = Vector2(40, 40)
text = "▶"
flat = true

[node name="TextureRect" type="TextureRect" parent="MainContainer/CardInfoArea/CardInfoContainer/RightScrollButton"]
modulate = Color(0.862745, 0.862745, 0.862745, 1)
custom_minimum_size = Vector2(100, 100)
layout_mode = 0
offset_left = -10.0
offset_top = -10.0
offset_right = 90.0
offset_bottom = 90.0
texture = ExtResource("3_8r0tg")
expand_mode = 1
stretch_mode = 5

[node name="BottomContainer" type="CenterContainer" parent="MainContainer"]
z_index = 1
custom_minimum_size = Vector2(0, 100)
layout_mode = 2

[node name="ReturnButton" type="Button" parent="MainContainer/BottomContainer"]
custom_minimum_size = Vector2(120, 30)
layout_mode = 2
theme_override_colors/font_color = Color(1, 1, 1, 1)
theme_override_font_sizes/font_size = 24
text = "RETURN"
flat = true

[node name="Back" type="TextureRect" parent="MainContainer/BottomContainer/ReturnButton"]
custom_minimum_size = Vector2(150, 50)
layout_mode = 2
offset_left = -15.0
offset_top = -4.0
offset_right = 135.0
offset_bottom = 46.0
texture = ExtResource("5_5e67x")
expand_mode = 1
stretch_mode = 5

[node name="ReturnLabel" type="Label" parent="MainContainer/BottomContainer/ReturnButton"]
layout_mode = 2
offset_left = 20.0
offset_top = 7.0
offset_right = 99.0
offset_bottom = 35.0
theme_override_font_sizes/font_size = 20
text = "RETURN"
horizontal_alignment = 1
vertical_alignment = 1

[node name="CardTooltip" type="Panel" parent="."]
visible = false
z_index = 100
layout_mode = 0
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -200.0
offset_top = -150.0
offset_right = 200.0
offset_bottom = 150.0
grow_horizontal = 2
grow_vertical = 2
theme_override_styles/panel = SubResource("StyleBoxFlat_1")

[node name="TooltipContainer" type="VBoxContainer" parent="CardTooltip"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = 20.0
offset_right = -20.0
offset_bottom = -20.0
grow_horizontal = 2
grow_vertical = 2

[node name="CardNameLabel" type="Label" parent="CardTooltip/TooltipContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 24
text = "卡牌名称"
horizontal_alignment = 1

[node name="HSeparator" type="HSeparator" parent="CardTooltip/TooltipContainer"]
layout_mode = 2

[node name="CardDescriptionLabel" type="RichTextLabel" parent="CardTooltip/TooltipContainer"]
layout_mode = 2
size_flags_vertical = 3
theme_override_font_sizes/normal_font_size = 18
bbcode_enabled = true
text = "卡牌描述"
fit_content = true
